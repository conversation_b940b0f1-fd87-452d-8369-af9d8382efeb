"""
Streamlined in-memory registry for a specific database
"""
import asyncio
import os
import time
from typing import Dict, Optional, Any, Set, List, Tuple
from ...logging import get_logger

# Import specialized managers
from .cache_manager import CacheManager
from .addon_manager import AddonManager
from .model_metadata_manager import ModelMetadataManager
from .route_manager import RouteManager


class AppRegistry:
    """
    Enhanced in-memory registry for a specific database
    Supports addon loading in installation order, model/field metadata, and query caching
    """

    def __init__(self, db_name: str):
        self.db_name = db_name
        self.created_at = time.time()

        # Core data structures
        self.installed_modules: Dict[str, Dict[str, Any]] = {}  # Module name -> module info
        self.environments: Set[Any] = set()  # Track environments using this registry

        # Initialize specialized managers
        self.cache_manager = CacheManager(db_name)
        self.addon_manager = AddonManager(db_name)
        self.model_metadata_manager = ModelMetadataManager(db_name)
        self.route_manager = RouteManager(db_name)

        # Unified addon loading state management
        self._addon_loading_state = "NOT_STARTED"  # NOT_STARTED, LOADING, LOADED, FAILED
        self._addon_load_order: List[str] = []
        self._loaded_addons: Set[str] = set()

        # Simplified locking - single lock for all operations
        self._lock = asyncio.Lock()
        self._logger = get_logger(f"{__name__}.{db_name}")

        self._logger.info(f"Created enhanced registry for database: {db_name}")

    # =============================================================================
    # INITIALIZATION AND SETUP METHODS
    # =============================================================================

    async def ensure_routes_registered(self):
        """Ensure routes are registered for this database (lazy loading)"""
        async with self._lock:
            if self._addon_loading_state == "LOADED":
                self._logger.debug(f"Routes already registered via unified loading for {self.db_name}")
                return
            elif self._addon_loading_state == "NOT_STARTED":
                await self._load_all_addons_unified()
                return


    async def refresh_from_database(self):
        """Refresh registry data from database"""
        try:
            from ..registry.database_registry import DatabaseRegistry
            db_manager = await DatabaseRegistry.get_database(self.db_name)

            # Clear and reload installed modules in installation order
            async with self._lock:
                self.installed_modules.clear()
                self.addon_manager.reset_addon_state()
                self.model_metadata_manager.clear_metadata()
                await self.cache_manager.clear_query_cache()
                # Reset unified addon loading state
                self._addon_loading_state = "NOT_STARTED"
                self._addon_load_order.clear()
                self._loaded_addons.clear()

            # Load modules in installation order (first installed, first loaded)
            modules_query = """
                SELECT name, display_name, version, state, action_at, create_at
                FROM ir_module_module
                WHERE state = 'installed'
                ORDER BY create_at ASC, action_at ASC
            """

            modules = await db_manager.fetch(modules_query)

            addon_order = []
            for module in modules:
                await self.register_module(module['name'], dict(module))
                addon_order.append(module['name'])

            # Store addon load order for unified loading
            async with self._lock:
                self._addon_load_order = addon_order.copy()

            # Load models and fields metadata
            await self.model_metadata_manager.load_models_metadata(db_manager)
            await self.model_metadata_manager.load_fields_metadata(db_manager)

            # UNIFIED ADDON LOADING - Single source of truth
            await self._load_all_addons_unified()

            self._logger.debug(f"Registry refreshed for {self.db_name}: {len(modules)} modules, {len(self.model_metadata_manager.models)} models")

        except Exception as e:
            self._logger.error(f"Failed to refresh registry for {self.db_name}: {e}")
            async with self._lock:
                self._addon_loading_state = "FAILED"

    async def _load_all_addons_unified(self) -> None:
        """
        UNIFIED ADDON LOADING - Single source of truth for all addon loading operations.
        This method consolidates all addon loading activities to prevent duplicates and ensure consistency.
        """
        async with self._lock:
            if self._addon_loading_state == "LOADED":
                self._logger.debug(f"Addons already loaded for {self.db_name}, skipping")
                return

            if self._addon_loading_state == "LOADING":
                self._logger.warning(f"Addon loading already in progress for {self.db_name}")
                return

            self._addon_loading_state = "LOADING"

        try:
            self._logger.debug(f"Starting unified addon loading for {self.db_name}: {self._addon_load_order}")

            # Step 1: Register addon paths for import system
            await self._register_addon_paths_unified()

            # Step 2: Import addon modules in order
            await self._import_addon_modules_unified()

            # Step 3: Discover and register routes
            await self._discover_routes_unified()

            # Step 4: Validate loaded modules against database
            await self._validate_loaded_modules()

            async with self._lock:
                self._addon_loading_state = "LOADED"

            self._logger.info(f"✅ Addon loading completed for {self.db_name}: {len(self._loaded_addons)} modules")

        except Exception as e:
            self._logger.error(f"Unified addon loading failed for {self.db_name}: {e}")
            async with self._lock:
                self._addon_loading_state = "FAILED"
            raise

    async def _register_addon_paths_unified(self) -> None:
        """Register addon paths with the import system - unified approach"""
        try:
            from ...addons import _addon_import_manager
            from ...config import config

            addon_paths = config.addons_paths
            registered_count = 0

            for addon_name in self._addon_load_order:
                if addon_name == 'base':  # Skip base module
                    continue

                # Find the addon in the configured paths
                for addons_path in addon_paths:
                    addon_path = os.path.join(addons_path, addon_name)
                    if os.path.exists(addon_path) and os.path.isdir(addon_path):
                        # Check if it has a manifest file
                        manifest_file = os.path.join(addon_path, '__manifest__.py')
                        if os.path.exists(manifest_file):
                            # Register the addon path
                            _addon_import_manager.register_addon_path(addon_name, addon_path)
                            registered_count += 1
                            break
                else:
                    self._logger.warning(f"Addon path not found for {addon_name}")

            self._logger.debug(f"Registered {registered_count} addon paths for {self.db_name}")

        except Exception as e:
            self._logger.error(f"Failed to register addon paths for {self.db_name}: {e}")
            raise

    async def _import_addon_modules_unified(self) -> None:
        """Import addon modules in installation order - unified approach"""
        try:
            import sys
            import importlib

            loaded_count = 0
            failed_count = 0

            for addon_name in self._addon_load_order:
                if addon_name == 'base':  # Skip base module
                    continue

                try:
                    module_name = f'erp.addons.{addon_name}'
                    if module_name not in sys.modules:
                        # Import main addon module
                        importlib.import_module(module_name)

                        # Import all Python modules in the addon directory recursively
                        await self._import_addon_modules_recursive(module_name, addon_name)

                    async with self._lock:
                        self._loaded_addons.add(addon_name)
                    loaded_count += 1

                except Exception as e:
                    self._logger.error(f"Failed to load addon {addon_name}: {e}")
                    failed_count += 1
                    continue

            if failed_count > 0:
                self._logger.warning(f"Module import completed for {self.db_name}: {loaded_count} loaded, {failed_count} failed")
            else:
                self._logger.debug(f"Module import completed for {self.db_name}: {loaded_count} loaded")

        except Exception as e:
            self._logger.error(f"Failed to import addon modules for {self.db_name}: {e}")
            raise

    async def _import_addon_modules_recursive(self, module_name: str, addon_name: str) -> None:
        """Import all Python modules in an addon directory recursively"""
        try:
            import sys
            import importlib
            from pathlib import Path
            from ...config import config

            # Find addon directory
            addon_path = None
            for addons_path in config.addons_paths:
                potential_path = os.path.join(addons_path, addon_name)
                if os.path.exists(potential_path) and os.path.isdir(potential_path):
                    addon_path = potential_path
                    break

            if not addon_path:
                self._logger.warning(f"Addon directory not found for {addon_name}")
                return

            addon_dir = Path(addon_path)
            for py_file in addon_dir.rglob("*.py"):
                if py_file.name.startswith('__'):
                    continue  # Skip __init__.py, __manifest__.py, etc.

                # Convert file path to module name
                relative_path = py_file.relative_to(addon_dir)
                module_parts = list(relative_path.parts[:-1]) + [relative_path.stem]
                sub_module_name = f'{module_name}.' + '.'.join(module_parts)

                # Import if not already imported
                if sub_module_name not in sys.modules:
                    try:
                        importlib.import_module(sub_module_name)
                        self._logger.debug(f"Imported sub-module: {sub_module_name}")
                    except Exception as e:
                        self._logger.debug(f"Failed to import sub-module {sub_module_name}: {e}")

        except Exception as e:
            self._logger.error(f"Failed to import addon modules recursively for {addon_name}: {e}")

    async def _discover_routes_unified(self) -> None:
        """Discover and register routes from loaded addons - unified approach"""
        try:
            # Use route manager but with our loaded addons list
            await self.route_manager.discover_routes_from_loaded_addons(list(self._loaded_addons))

        except Exception as e:
            self._logger.error(f"Failed to discover routes for {self.db_name}: {e}")
            raise



    # =============================================================================
    # ENVIRONMENT MANAGEMENT METHODS
    # =============================================================================

    async def register_environment(self, env):
        """Register an environment with this registry"""
        async with self._lock:
            self.environments.add(env)

    async def unregister_environment(self, env):
        """Unregister an environment from this registry"""
        async with self._lock:
            self.environments.discard(env)

    def get_active_environments_count(self) -> int:
        """Get count of active environments"""
        return len(self.environments)

    # =============================================================================
    # MODULE MANAGEMENT METHODS
    # =============================================================================

    async def register_module(self, module_name: str, module_info: Dict[str, Any]):
        """Register an installed module in the registry"""
        async with self._lock:
            self.installed_modules[module_name] = {
                'name': module_name,
                'display_name': module_info.get('display_name', module_name),
                'version': module_info.get('version', '1.0.0'),
                'state': module_info.get('state', 'installed'),
                'registered_at': time.time()
            }

    async def get_installed_modules(self) -> Dict[str, Dict[str, Any]]:
        """Get all installed modules"""
        async with self._lock:
            return self.installed_modules.copy()

    async def update_registry_after_module_action(self, module_name: str, action: str) -> None:
        """
        Update registry after a module action (install/uninstall/upgrade)
        This method completely reinitializes the AppRegistry

        Args:
            module_name: Name of the module that was acted upon
            action: Action performed (install/uninstall/upgrade)
        """
        try:
            self._logger.info(f"Updating registry after {action} of module '{module_name}' for database '{self.db_name}'")

            # Clear all current state
            async with self._lock:
                self.installed_modules.clear()
                self.addon_manager.reset_addon_state()
                self.model_metadata_manager.clear_metadata()
                await self.cache_manager.clear_query_cache()
                await self.route_manager.clear_routes()

            # Completely reinitialize from database
            await self.refresh_from_database()

            # Ensure routes are registered after reinitialization
            await self.ensure_routes_registered()

            self._logger.info(f"Registry successfully updated after {action} of module '{module_name}' for database '{self.db_name}'")

        except Exception as e:
            self._logger.error(f"Failed to update registry after {action} of module '{module_name}' for database '{self.db_name}': {e}")
            raise

    # =============================================================================
    # ADDON MANAGEMENT METHODS (Delegated to AddonManager)
    # =============================================================================

    async def get_addon_load_order(self) -> List[str]:
        """Get the addon load order"""
        async with self._lock:
            return self._addon_load_order.copy()

    async def get_addon_loading_state(self) -> str:
        """Get the current addon loading state"""
        async with self._lock:
            return self._addon_loading_state

    async def get_loaded_addons(self) -> Set[str]:
        """Get the set of successfully loaded addons"""
        async with self._lock:
            return self._loaded_addons.copy()

    # =============================================================================
    # ROUTE MANAGEMENT METHODS (Delegated to RouteManager)
    # =============================================================================

    async def register_route(self, path: str, handler: Any, **kwargs):
        """Register a route handler for this database"""
        await self.route_manager.register_route(path, handler, **kwargs)

    async def get_routes(self) -> Dict[str, Dict]:
        """Get all registered routes for this database"""
        return await self.route_manager.get_routes()

    async def get_route(self, path: str) -> Optional[Dict]:
        """Get specific route by path"""
        return await self.route_manager.get_route(path)

    async def remove_route(self, path: str) -> bool:
        """Remove a route by path"""
        return await self.route_manager.remove_route(path)

    async def clear_routes(self):
        """Clear all routes for this database"""
        await self.route_manager.clear_routes()

    # =============================================================================
    # MODEL METADATA METHODS (Delegated to ModelMetadataManager)
    # =============================================================================

    async def get_model_metadata(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get metadata for a specific model"""
        return await self.model_metadata_manager.get_model_metadata(model_name)

    async def get_all_models_metadata(self) -> Dict[str, Dict[str, Any]]:
        """Get metadata for all models"""
        return await self.model_metadata_manager.get_all_models_metadata()

    async def get_model_fields_metadata(self, model_name: str) -> Optional[Dict[str, Dict[str, Any]]]:
        """Get field metadata for a specific model"""
        return await self.model_metadata_manager.get_model_fields_metadata(model_name)

    async def get_field_metadata(self, model_name: str, field_name: str) -> Optional[Dict[str, Any]]:
        """Get metadata for a specific field of a model"""
        return await self.model_metadata_manager.get_field_metadata(model_name, field_name)

    async def get_all_fields_metadata(self) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """Get metadata for all fields of all models"""
        return await self.model_metadata_manager.get_all_fields_metadata()

    async def get_model_data(self, model_name: str, use_cache: bool = True) -> Optional[Dict[str, Any]]:
        """Get model data with query cache support"""
        if use_cache:
            cache_key = f"model_data:{model_name}"
            cached_data = await self.cache_manager.get_cached_query(cache_key)
            if cached_data is not None:
                return cached_data

        # Get model data from metadata manager
        model_data = await self.model_metadata_manager.get_model_data(model_name)

        # Cache the result if caching is enabled
        if use_cache and model_data:
            cache_key = f"model_data:{model_name}"
            await self.cache_manager.cache_query(cache_key, model_data)

        return model_data

    async def set_model_data(self, model_name: str, model_data: Dict[str, Any]) -> None:
        """Set model data and update cache"""
        await self.model_metadata_manager.set_model_data(model_name, model_data)
        # Invalidate cache
        await self.cache_manager.invalidate_model_cache(model_name)

    # =============================================================================
    # CACHE MANAGEMENT METHODS (Delegated to CacheManager)
    # =============================================================================

    async def cache_query(self, query_key: str, result: Any) -> None:
        """Cache a query result"""
        await self.cache_manager.cache_query(query_key, result)

    async def get_cached_query(self, query_key: str) -> Optional[Any]:
        """Get a cached query result"""
        return await self.cache_manager.get_cached_query(query_key)

    async def clear_query_cache(self) -> None:
        """Clear the query cache"""
        await self.cache_manager.clear_query_cache()

    async def invalidate_model_cache(self, model_name: str) -> None:
        """Invalidate cache for a specific model"""
        await self.cache_manager.invalidate_model_cache(model_name)

    async def execute_query_cached(self, query_key: str, query_func, *args, **kwargs) -> Any:
        """Execute a query with caching"""
        return await self.cache_manager.execute_query_cached(query_key, query_func, *args, **kwargs)

    async def _validate_loaded_modules(self) -> None:
        """
        Validate that loaded modules match installed modules in ir.module.module table.
        Throws exception if mismatch is detected.
        """
        try:
            from ..registry.database_registry import DatabaseRegistry
            db_manager = await DatabaseRegistry.get_database(self.db_name)

            # Get installed modules from database
            modules_query = """
                SELECT name FROM ir_module_module
                WHERE state = 'installed'
                ORDER BY name
            """
            installed_modules_db = await db_manager.fetch(modules_query)
            installed_module_names = {module['name'] for module in installed_modules_db}

            # Get loaded modules from registry (excluding base which is handled separately)
            loaded_module_names = set(self._loaded_addons)
            if 'base' in installed_module_names:
                loaded_module_names.add('base')  # Base is always considered loaded

            # Compare counts and names
            installed_count = len(installed_module_names)
            loaded_count = len(loaded_module_names)

            if installed_count != loaded_count:
                missing_modules = installed_module_names - loaded_module_names
                extra_modules = loaded_module_names - installed_module_names

                error_msg = (
                    f"Module validation failed for database '{self.db_name}': "
                    f"Installed modules ({installed_count}) != Loaded modules ({loaded_count}). "
                )

                if missing_modules:
                    error_msg += f"Missing modules: {sorted(missing_modules)}. "
                if extra_modules:
                    error_msg += f"Extra modules: {sorted(extra_modules)}. "

                self._logger.error(error_msg)
                raise RuntimeError(error_msg)

            # Validate individual module names match
            if installed_module_names != loaded_module_names:
                missing_modules = installed_module_names - loaded_module_names
                extra_modules = loaded_module_names - installed_module_names

                error_msg = (
                    f"Module name mismatch for database '{self.db_name}': "
                    f"Missing: {sorted(missing_modules)}, Extra: {sorted(extra_modules)}"
                )

                self._logger.error(error_msg)
                raise RuntimeError(error_msg)

            self._logger.info(f"✅ Module validation passed for {self.db_name}: {loaded_count} modules loaded successfully")

        except Exception as e:
            if isinstance(e, RuntimeError):
                raise  # Re-raise validation errors
            self._logger.error(f"Failed to validate modules for {self.db_name}: {e}")
            raise RuntimeError(f"Module validation failed due to error: {e}")

    # =============================================================================
    # DEPRECATED METHODS (For backward compatibility)
    # =============================================================================

    async def _ensure_addon_paths_registered(self, addon_names: List[str]) -> None:
        """DEPRECATED: Use unified loading instead"""
        self._logger.warning(f"DEPRECATED: _ensure_addon_paths_registered called for {self.db_name}. Use unified loading instead.")
        # This method is deprecated and does nothing - unified loading handles path registration

    # =============================================================================
    # UTILITY AND STATISTICS METHODS
    # =============================================================================

    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive registry statistics"""
        route_stats = self.route_manager.get_route_stats()
        cache_stats = self.cache_manager.get_cache_stats()
        metadata_stats = self.model_metadata_manager.get_metadata_stats()

        return {
            'db_name': self.db_name,
            'created_at': self.created_at,
            'installed_modules_count': len(self.installed_modules),
            'active_environments': self.get_active_environments_count(),
            'uptime_seconds': time.time() - self.created_at,
            'routes': route_stats,
            'cache': cache_stats,
            'metadata': metadata_stats
        }
